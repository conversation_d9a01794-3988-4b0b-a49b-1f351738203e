'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var module$1 = require('module');
var url = require('url');
var path = require('path');

/**
 * @fileoverview Universal module importer
 */

//-----------------------------------------------------------------------------
// Helpers
//-----------------------------------------------------------------------------

const __filename$1 = url.fileURLToPath((typeof document === 'undefined' ? new (require('u' + 'rl').URL)('file:' + __filename).href : (document.currentScript && document.currentScript.src || new URL('module-importer.cjs', document.baseURI).href)));
const __dirname$1 = path.dirname(__filename$1);
const require$1 = module$1.createRequire(__dirname$1 + "/");
const { ModuleImporter } = require$1("./module-importer.cjs");

exports.ModuleImporter = ModuleImporter;
