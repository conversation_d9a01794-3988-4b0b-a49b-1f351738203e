import { createRouter, createWebHistory } from "vue-router";
import { useUserStore } from "../store/modules/user";
import { message } from "ant-design-vue";

// 路由配置
const routes = [
  {
    path: "/",
    redirect: "/home",
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/auth/Login.vue"),
    meta: {
      title: "考生登录",
      requiresAuth: false,
    },
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("../views/home/<USER>"),
    meta: {
      title: "首页",
      requiresAuth: true,
    },
  },
  {
    path: "/profile",
    name: "Profile",
    component: () => import("../views/profile/Profile.vue"),
    meta: {
      title: "个人信息",
      requiresAuth: true,
    },
  },
  // 404页面
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/error/404.vue"),
    meta: {
      title: "页面不存在",
      requiresAuth: false,
    },
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_, __, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 全局前置守卫
router.beforeEach((to, _, next) => {
  const userStore = useUserStore();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 台湾学生申请系统`;
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (userStore.getIsLoggedIn) {
      next();
    } else {
      message.warning("请先登录");
      next("/login");
    }
  } else {
    // 如果已登录且访问登录页，重定向到首页
    if (to.path === "/login" && userStore.getIsLoggedIn) {
      next("/home");
    } else {
      next();
    }
  }
});

// 全局后置守卫
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`路由跳转: ${from.path} -> ${to.path}`);
});

export default router;
