<template>
  <div class="profile-container">
    <!-- 头部导航 -->
    <div class="profile-header">
      <a-button type="text" @click="goBack">
        <ArrowLeftOutlined />
        返回首页
      </a-button>
      <h2>个人信息</h2>
      <div></div>
    </div>

    <!-- 个人信息内容 -->
    <div class="profile-content">
      <a-card title="基本信息" :bordered="false">
        <a-form
          ref="formRef"
          :model="profileForm"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="姓名" name="actualName">
                <a-input v-model:value="profileForm.actualName" placeholder="请输入姓名" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="性别" name="gender">
                <a-select v-model:value="profileForm.gender" placeholder="请选择性别">
                  <a-select-option :value="1">男</a-select-option>
                  <a-select-option :value="2">女</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="登录邮箱" name="loginName">
                <a-input v-model:value="profileForm.loginName" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="手机号码" name="phone">
                <a-input v-model:value="profileForm.phone" placeholder="请输入手机号码" />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="备注" name="remark">
                <a-textarea
                  v-model:value="profileForm.remark"
                  placeholder="请输入备注信息"
                  :rows="4"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
            <a-space>
              <a-button type="primary" @click="handleSave" :loading="saveLoading">
                保存修改
              </a-button>
              <a-button @click="handleReset">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 登录信息 -->
      <a-card title="登录信息" :bordered="false" style="margin-top: 24px;">
        <a-descriptions :column="2">
          <a-descriptions-item label="上次登录时间">
            {{ formatLastLoginTime }}
          </a-descriptions-item>
          <a-descriptions-item label="上次登录IP">
            {{ userInfo.lastLoginIp || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="上次登录地区">
            {{ userInfo.lastLoginIpRegion || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="用户代理">
            {{ userInfo.lastLoginUserAgent || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 密码修改 -->
      <a-card title="密码修改" :bordered="false" style="margin-top: 24px;">
        <a-form
          ref="passwordFormRef"
          :model="passwordForm"
          :rules="passwordRules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="当前密码" name="oldPassword">
                <a-input-password
                  v-model:value="passwordForm.oldPassword"
                  placeholder="请输入当前密码"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="新密码" name="newPassword">
                <a-input-password
                  v-model:value="passwordForm.newPassword"
                  placeholder="请输入新密码"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="确认密码" name="confirmPassword">
                <a-input-password
                  v-model:value="passwordForm.confirmPassword"
                  placeholder="请再次输入新密码"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
            <a-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
              修改密码
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '../../store/modules/user';
import dayjs from 'dayjs';

const router = useRouter();
const userStore = useUserStore();

// 表单引用
const formRef = ref();
const passwordFormRef = ref();

// 用户信息
const userInfo = computed(() => userStore.userInfo);

// 个人信息表单
const profileForm = reactive({
  actualName: '',
  gender: null,
  loginName: '',
  phone: '',
  remark: ''
});

// 密码修改表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 状态变量
const saveLoading = ref(false);
const passwordLoading = ref(false);

// 表单验证规则
const rules = {
  actualName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (value && value !== passwordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

// 格式化上次登录时间
const formatLastLoginTime = computed(() => {
  if (userInfo.value.lastLoginTime) {
    return dayjs(userInfo.value.lastLoginTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return '-';
});

// 页面初始化
onMounted(() => {
  initProfileForm();
});

// 初始化表单数据
const initProfileForm = () => {
  const info = userInfo.value;
  profileForm.actualName = info.actualName || '';
  profileForm.gender = info.gender || null;
  profileForm.loginName = info.loginName || '';
  profileForm.phone = info.phone || '';
  profileForm.remark = info.remark || '';
};

// 返回首页
const goBack = () => {
  router.push('/home');
};

// 保存个人信息
const handleSave = async () => {
  try {
    await formRef.value.validate();
    saveLoading.value = true;
    
    // 这里应该调用更新个人信息的API
    // await userApi.updateProfile(profileForm);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success('个人信息保存成功');
    
    // 更新store中的用户信息
    userStore.updateUserInfo(profileForm);
    
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单信息');
    } else {
      message.error('保存失败，请重试');
    }
  } finally {
    saveLoading.value = false;
  }
};

// 重置表单
const handleReset = () => {
  initProfileForm();
  message.info('表单已重置');
};

// 修改密码
const handleChangePassword = async () => {
  try {
    await passwordFormRef.value.validate();
    passwordLoading.value = true;
    
    // 这里应该调用修改密码的API
    // await userApi.changePassword(passwordForm);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    message.success('密码修改成功');
    
    // 清空密码表单
    passwordForm.oldPassword = '';
    passwordForm.newPassword = '';
    passwordForm.confirmPassword = '';
    
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查密码信息');
    } else {
      message.error('密码修改失败，请重试');
    }
  } finally {
    passwordLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.profile-header {
  background: white;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;

  h2 {
    margin: 0;
    color: #333;
    font-weight: 600;
  }

  .ant-btn {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.profile-content {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;

  .ant-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    :deep(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      
      .ant-card-head-title {
        font-size: 18px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 32px;
    }
  }
}

@media (max-width: 768px) {
  .profile-content {
    padding: 16px;
  }

  .ant-card {
    :deep(.ant-card-body) {
      padding: 24px 16px;
    }
  }

  .ant-form {
    :deep(.ant-form-item-label) {
      text-align: left;
    }
  }
}
</style>
