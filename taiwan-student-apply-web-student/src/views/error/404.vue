<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-icon">
        <FileSearchOutlined />
      </div>
      <h1 class="error-title">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      <div class="error-actions">
        <a-button type="primary" @click="goHome">
          返回首页
        </a-button>
        <a-button @click="goBack">
          返回上页
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { FileSearchOutlined } from '@ant-design/icons-vue';

const router = useRouter();

const goHome = () => {
  router.push('/home');
};

const goBack = () => {
  router.go(-1);
};
</script>

<style lang="less" scoped>
.error-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
}

.error-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 90vw;
}

.error-icon {
  font-size: 80px;
  color: #ccc;
  margin-bottom: 20px;
}

.error-title {
  font-size: 72px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.error-message {
  font-size: 18px;
  color: #666;
  margin-bottom: 32px;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
