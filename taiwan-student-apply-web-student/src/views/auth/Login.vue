<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-title">考生登录</div>
      <a-form
        ref="formRef"
        :model="loginForm"
        :rules="rules"
        @finish="handleLogin"
        class="login-form"
      >
        <a-form-item name="loginName">
          <a-input
            v-model:value="loginForm.loginName"
            placeholder="请输入邮箱"
            size="large"
            @blur="checkNeedCaptcha"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item name="password">
          <a-input-password
            v-model:value="loginForm.password"
            placeholder="请输入密码"
            size="large"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <!-- 验证码 -->
        <a-form-item name="captchaCode" v-if="showCaptcha">
          <div class="captcha-container">
            <a-input
              v-model:value="loginForm.captchaCode"
              placeholder="请输入验证码"
              size="large"
              class="captcha-input"
            />
            <img
              :src="captchaImage"
              @click="getCaptcha"
              class="captcha-img"
              alt="验证码"
              title="点击刷新验证码"
            />
          </div>
        </a-form-item>

        <!-- 邮箱验证码 -->
        <a-form-item name="emailCode" v-if="showEmailCode">
          <div class="email-code-container">
            <a-input
              v-model:value="loginForm.emailCode"
              placeholder="请输入邮箱验证码"
              size="large"
              class="email-code-input"
            />
            <a-button
              @click="sendEmailCode"
              :disabled="emailCodeDisabled"
              :loading="emailCodeLoading"
              size="large"
            >
              {{ emailCodeText }}
            </a-button>
          </div>
        </a-form-item>

        <a-form-item>
          <div class="login-options">
            <a-checkbox v-model:checked="rememberPassword">
              记住密码
            </a-checkbox>
          </div>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loginLoading"
            class="login-btn"
          >
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import { authApi } from '../../api/auth';
import { useUserStore } from '../../store/modules/user';
import { encryptData } from '../../utils/encrypt';
import { LOGIN_DEVICE_ENUM } from '../../constants';

const router = useRouter();
const userStore = useUserStore();

// 表单引用
const formRef = ref();

// 登录表单
const loginForm = reactive({
  loginName: '',
  password: '',
  captchaCode: '',
  captchaUuid: '',
  emailCode: '',
  loginDevice: LOGIN_DEVICE_ENUM.PC.value
});

// 表单验证规则
const rules = {
  loginName: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位', trigger: 'blur' }
  ]
};

// 状态变量
const loginLoading = ref(false);
const showCaptcha = ref(false);
const showEmailCode = ref(false);
const captchaImage = ref('');
const rememberPassword = ref(false);

// 邮箱验证码相关
const emailCodeDisabled = ref(false);
const emailCodeLoading = ref(false);
const emailCodeText = ref('获取验证码');
let emailCodeTimer = null;

// 页面加载时的初始化
onMounted(() => {
  // 检查是否有记住的密码
  const savedPassword = localStorage.getItem('student_remember_password');
  if (savedPassword) {
    const { loginName, password } = JSON.parse(savedPassword);
    loginForm.loginName = loginName;
    loginForm.password = password;
    rememberPassword.value = true;
  }
});

// 检查是否需要验证码
const checkNeedCaptcha = async () => {
  if (!loginForm.loginName) return;
  
  try {
    const res = await authApi.checkNeedCaptcha(loginForm.loginName);
    showCaptcha.value = res.data;
    if (showCaptcha.value) {
      getCaptcha();
      // 动态添加验证码验证规则
      rules.captchaCode = [{ required: true, message: '请输入验证码', trigger: 'blur' }];
    } else {
      // 移除验证码验证规则
      delete rules.captchaCode;
    }
  } catch (error) {
    console.error('检查验证码需求失败:', error);
  }
};

// 获取验证码
const getCaptcha = async () => {
  try {
    const res = await authApi.getCaptcha();
    captchaImage.value = res.data.captchaBase64Image;
    loginForm.captchaUuid = res.data.captchaUuid;
  } catch (error) {
    message.error('获取验证码失败');
  }
};

// 发送邮箱验证码
const sendEmailCode = async () => {
  if (!loginForm.loginName) {
    message.warning('请先输入邮箱');
    return;
  }

  try {
    emailCodeLoading.value = true;
    await authApi.sendEmailCode(loginForm.loginName);
    message.success('验证码已发送到您的邮箱');
    
    // 开始倒计时
    startEmailCodeCountdown();
  } catch (error) {
    message.error('发送验证码失败');
  } finally {
    emailCodeLoading.value = false;
  }
};

// 邮箱验证码倒计时
const startEmailCodeCountdown = () => {
  let countdown = 60;
  emailCodeDisabled.value = true;
  emailCodeText.value = `${countdown}秒后重新获取`;
  
  emailCodeTimer = setInterval(() => {
    countdown--;
    if (countdown > 0) {
      emailCodeText.value = `${countdown}秒后重新获取`;
    } else {
      clearInterval(emailCodeTimer);
      emailCodeDisabled.value = false;
      emailCodeText.value = '获取验证码';
    }
  }, 1000);
};

// 处理登录
const handleLogin = async () => {
  try {
    loginLoading.value = true;
    
    // 加密密码
    const encryptedPassword = encryptData(loginForm.password);
    
    const loginData = {
      ...loginForm,
      password: encryptedPassword
    };
    
    const res = await authApi.login(loginData);
    
    // 保存用户信息
    userStore.setUserInfo(res.data);
    
    // 记住密码
    if (rememberPassword.value) {
      localStorage.setItem('student_remember_password', JSON.stringify({
        loginName: loginForm.loginName,
        password: loginForm.password
      }));
    } else {
      localStorage.removeItem('student_remember_password');
    }
    
    message.success('登录成功');
    router.push('/home');
    
  } catch (error) {
    // 登录失败后重新检查验证码需求
    if (showCaptcha.value) {
      getCaptcha();
      loginForm.captchaCode = '';
    }
  } finally {
    loginLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90vw;
}

.login-title {
  text-align: center;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 30px;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-form {
  .ant-form-item {
    margin-bottom: 20px;
  }
}

.captcha-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.captcha-input {
  flex: 1;
}

.captcha-img {
  width: 100px;
  height: 40px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    border-color: #40a9ff;
  }
}

.email-code-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.email-code-input {
  flex: 1;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 16px;
  font-weight: 600;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
}
</style>
