<template>
  <div class="home-container">
    <!-- 头部导航 -->
    <div class="home-header">
      <div class="header-left">
        <h2>台湾学生申请系统</h2>
      </div>
      <div class="header-right">
        <a-dropdown>
          <a-button type="text">
            <UserOutlined />
            {{ userInfo.actualName || '考生' }}
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile" @click="goToProfile">
                <UserOutlined />
                个人信息
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout" @click="handleLogout">
                <LogoutOutlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="home-content">
      <!-- 欢迎卡片 -->
      <div class="welcome-card">
        <h3>欢迎，{{ userInfo.actualName || '考生' }}！</h3>
        <p>您好，欢迎使用台湾学生申请系统。请选择下方功能进行操作。</p>
        <div class="user-info">
          <a-row :gutter="16">
            <a-col :span="8">
              <div class="info-item">
                <span class="label">登录邮箱：</span>
                <span class="value">{{ userInfo.loginName || '-' }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">手机号码：</span>
                <span class="value">{{ userInfo.phone || '-' }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="info-item">
                <span class="label">上次登录：</span>
                <span class="value">{{ formatLastLoginTime }}</span>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 功能模块 -->
      <div class="function-grid">
        <div class="function-card" @click="goToApplication">
          <div class="function-icon">
            <FileTextOutlined />
          </div>
          <div class="function-title">申请管理</div>
          <div class="function-desc">查看和管理您的申请信息</div>
        </div>

        <div class="function-card" @click="goToProfile">
          <div class="function-icon">
            <UserOutlined />
          </div>
          <div class="function-title">个人信息</div>
          <div class="function-desc">完善和修改个人基本信息</div>
        </div>

        <div class="function-card" @click="goToDocuments">
          <div class="function-icon">
            <FolderOutlined />
          </div>
          <div class="function-title">材料上传</div>
          <div class="function-desc">上传申请所需的各类材料</div>
        </div>

        <div class="function-card" @click="goToMessages">
          <div class="function-icon">
            <BellOutlined />
          </div>
          <div class="function-title">消息通知</div>
          <div class="function-desc">查看系统通知和重要消息</div>
        </div>

        <div class="function-card" @click="goToHelp">
          <div class="function-icon">
            <QuestionCircleOutlined />
          </div>
          <div class="function-title">帮助中心</div>
          <div class="function-desc">查看常见问题和操作指南</div>
        </div>

        <div class="function-card" @click="goToSettings">
          <div class="function-icon">
            <SettingOutlined />
          </div>
          <div class="function-title">系统设置</div>
          <div class="function-desc">修改密码和其他系统设置</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import {
  UserOutlined,
  DownOutlined,
  LogoutOutlined,
  FileTextOutlined,
  FolderOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';
import { useUserStore } from '../../store/modules/user';
import dayjs from 'dayjs';

const router = useRouter();
const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => userStore.userInfo);

// 格式化上次登录时间
const formatLastLoginTime = computed(() => {
  if (userInfo.value.lastLoginTime) {
    return dayjs(userInfo.value.lastLoginTime).format('YYYY-MM-DD HH:mm');
  }
  return '-';
});

// 页面初始化
onMounted(() => {
  // 获取最新的用户信息
  userStore.getUserInfo();
});

// 导航方法
const goToProfile = () => {
  router.push('/profile');
};

const goToApplication = () => {
  message.info('申请管理功能开发中...');
};

const goToDocuments = () => {
  message.info('材料上传功能开发中...');
};

const goToMessages = () => {
  message.info('消息通知功能开发中...');
};

const goToHelp = () => {
  message.info('帮助中心功能开发中...');
};

const goToSettings = () => {
  message.info('系统设置功能开发中...');
};

// 退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await userStore.logout();
        message.success('退出登录成功');
        router.push('/login');
      } catch (error) {
        message.error('退出登录失败');
      }
    }
  });
};
</script>

<style lang="less" scoped>
.home-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.home-header {
  background: white;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;

  .header-left {
    h2 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }
  }

  .header-right {
    .ant-btn {
      height: 40px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.home-content {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;

  h3 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 16px;
    margin-bottom: 24px;
  }

  .user-info {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .label {
        color: #666;
        font-weight: 500;
        min-width: 80px;
      }

      .value {
        color: #333;
        font-weight: 600;
      }
    }
  }
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.function-card {
  background: white;
  padding: 32px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
  }

  .function-icon {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 16px;
    transition: all 0.3s ease;
  }

  .function-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
  }

  .function-desc {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }

  &:hover .function-icon {
    color: #5a6fd8;
    transform: scale(1.1);
  }
}

@media (max-width: 768px) {
  .home-content {
    padding: 16px;
  }

  .welcome-card {
    padding: 24px 16px;
  }

  .function-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .function-card {
    padding: 24px 16px;
  }
}
</style>
