import { defineStore } from 'pinia';
import { authApi } from '../../api/auth';
import { STORAGE_KEYS } from '../../constants';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem(STORAGE_KEYS.TOKEN) || '',
    userInfo: JSON.parse(localStorage.getItem(STORAGE_KEYS.USER_INFO) || '{}'),
    isLoggedIn: false
  }),

  getters: {
    // 是否已登录
    getIsLoggedIn: (state) => {
      return !!state.token && !!state.userInfo.studentId;
    },

    // 用户姓名
    getUserName: (state) => {
      return state.userInfo.actualName || '考生';
    },

    // 用户邮箱
    getUserEmail: (state) => {
      return state.userInfo.loginName || '';
    }
  },

  actions: {
    // 设置用户信息
    setUserInfo(data) {
      this.token = data.token;
      this.userInfo = data;
      this.isLoggedIn = true;

      // 保存到本地存储
      localStorage.setItem(STORAGE_KEYS.TOKEN, data.token);
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(data));
    },

    // 更新用户信息
    updateUserInfo(data) {
      this.userInfo = { ...this.userInfo, ...data };
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(this.userInfo));
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await authApi.getLoginInfo();
        this.userInfo = res.data;
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(res.data));
        return res.data;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        throw error;
      }
    },

    // 初始化用户信息
    initUserInfo() {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);

      if (token && userInfo) {
        this.token = token;
        this.userInfo = JSON.parse(userInfo);
        this.isLoggedIn = true;
      }
    },

    // 退出登录
    async logout() {
      try {
        // 调用后端退出接口
        await authApi.logout();
      } catch (error) {
        console.error('退出登录接口调用失败:', error);
      } finally {
        // 清除本地数据
        this.clearUserData();
      }
    },

    // 清除用户数据
    clearUserData() {
      this.token = '';
      this.userInfo = {};
      this.isLoggedIn = false;

      // 清除本地存储
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    }
  }
});
