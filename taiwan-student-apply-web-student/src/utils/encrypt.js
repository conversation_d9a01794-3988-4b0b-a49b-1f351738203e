import { sm2 } from 'sm-crypto';

// SM2 公钥 (这里使用示例公钥，实际项目中应该从后端获取)
const PUBLIC_KEY = '04298364ec840088c0dd2037c0e8d78366447ec5e0a8b6c5c8f8e0c4b2d8c9a8e7f1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1';

/**
 * SM2 加密
 * @param {string} data 要加密的数据
 * @returns {string} 加密后的数据
 */
export function encryptData(data) {
  try {
    return sm2.doEncrypt(data, PUBLIC_KEY, 1);
  } catch (error) {
    console.error('加密失败:', error);
    return data; // 加密失败时返回原数据
  }
}

/**
 * 简单的 Base64 编码（备用方案）
 * @param {string} data 要编码的数据
 * @returns {string} 编码后的数据
 */
export function base64Encode(data) {
  try {
    return btoa(encodeURIComponent(data));
  } catch (error) {
    console.error('Base64编码失败:', error);
    return data;
  }
}