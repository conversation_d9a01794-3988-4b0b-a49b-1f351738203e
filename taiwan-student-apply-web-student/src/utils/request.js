import axios from "axios";
import { message } from "ant-design-vue";
import { useUserStore } from "../store/modules/user";
import router from "../router";

// 创建 axios 实例
const request = axios.create({
  baseURL: "/api",
  timeout: 10000,
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加 token
    const userStore = useUserStore();
    const token = userStore.token;
    if (token) {
      config.headers["x-access-token"] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 如果返回的状态码为1，说明接口请求成功，可以正常拿到数据
    if (res.code === 1) {
      return res;
    }

    // 如果返回的状态码不为1，说明接口请求失败
    if (res.code === 0) {
      message.error(res.msg || "请求失败");
      return Promise.reject(new Error(res.msg || "请求失败"));
    }

    // 其他错误
    message.error(res.msg || "未知错误");
    return Promise.reject(new Error(res.msg || "未知错误"));
  },
  (error) => {
    console.error("请求错误:", error);

    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          message.error("登录已过期，请重新登录");
          const userStore = useUserStore();
          userStore.logout();
          router.push("/login");
          break;
        case 403:
          message.error("没有权限访问");
          break;
        case 404:
          message.error("请求的资源不存在");
          break;
        case 500:
          message.error("服务器内部错误");
          break;
        default:
          message.error(data?.msg || `请求失败 (${status})`);
      }
    } else if (error.request) {
      message.error("网络连接失败，请检查网络");
    } else {
      message.error("请求配置错误");
    }

    return Promise.reject(error);
  }
);

export default request;
