import request from '../utils/request';

/**
 * 考生登录 API
 */
export const authApi = {
  /**
   * 登录
   * @param {Object} data 登录表单数据
   * @returns {Promise}
   */
  login(data) {
    return request({
      url: '/login',
      method: 'post',
      data
    });
  },

  /**
   * 退出登录
   * @returns {Promise}
   */
  logout() {
    return request({
      url: '/login/logout',
      method: 'get'
    });
  },

  /**
   * 获取验证码
   * @returns {Promise}
   */
  getCaptcha() {
    return request({
      url: '/login/getCaptcha',
      method: 'get'
    });
  },

  /**
   * 获取登录信息
   * @returns {Promise}
   */
  getLoginInfo() {
    return request({
      url: '/login/getLoginInfo',
      method: 'get'
    });
  },

  /**
   * 发送邮箱验证码
   * @param {string} email 邮箱地址
   * @returns {Promise}
   */
  sendEmailCode(email) {
    return request({
      url: `/login/sendEmailCode/${email}`,
      method: 'get'
    });
  },

  /**
   * 检查是否需要验证码
   * @param {string} loginName 登录名
   * @returns {Promise}
   */
  checkNeedCaptcha(loginName) {
    return request({
      url: `/login/checkNeedCaptcha/${loginName}`,
      method: 'get'
    });
  }
};
