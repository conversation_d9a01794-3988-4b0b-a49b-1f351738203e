{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "paths": {"/@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}