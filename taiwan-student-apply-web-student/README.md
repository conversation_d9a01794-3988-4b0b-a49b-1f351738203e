# 台湾学生申请系统 - 考生端

## 项目介绍

台湾学生申请系统的考生端前端项目，基于 Vue 3 + Vite + Ant Design Vue 构建。

## 功能特性

- 考生登录/注册
- 个人信息管理
- 申请流程管理
- 文件上传下载
- 消息通知

## 技术栈

- Vue 3
- Vite
- Ant Design Vue
- Vue Router
- Pinia
- Axios

## 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build:prod
```

## 项目结构

```
src/
├── api/           # API 接口
├── assets/        # 静态资源
├── components/    # 公共组件
├── config/        # 配置文件
├── constants/     # 常量定义
├── router/        # 路由配置
├── store/         # 状态管理
├── utils/         # 工具函数
├── views/         # 页面组件
├── App.vue        # 根组件
└── main.js        # 入口文件
```

## 开发规范

请参考项目根目录下的编码规范文档。
