package cn.edu.xmut.tsa.admin.module.enrollment.domain.form;

import cn.edu.xmut.tsa.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 招生计划查询表单
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "招生计划查询表单")
public class EnrollmentPlanQueryForm extends PageParam {

    /**
     * 招生年度
     */
    @Schema(description = "招生年度")
    private Integer year;

    /**
     * 科类
     */
    @Schema(description = "科类")
    private Integer subjectType;

    /**
     * 专业名称
     */
    @Schema(description = "专业名称")
    private String majorName;

    /**
     * 所属学院
     */
    @Schema(description = "所属学院")
    private String collegeName;
}
