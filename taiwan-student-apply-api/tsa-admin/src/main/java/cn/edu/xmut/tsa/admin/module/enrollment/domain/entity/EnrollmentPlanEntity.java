package cn.edu.xmut.tsa.admin.module.enrollment.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 招生计划 实体类
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
@TableName("t_enrollment_plan")
public class EnrollmentPlanEntity {

    /**
     * 招生计划ID
     */
    @TableId(type = IdType.AUTO)
    private Long enrollmentPlanId;

    /**
     * 招生年度
     */
    private Integer year;

    /**
     * 科类
     */
    private Integer subjectType;

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 所属学院
     */
    private String collegeName;

    /**
     * 学制（年）
     */
    private Integer educationDuration;

    /**
     * 备注
     */
    private String remark;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否删除：0-否，1-是
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
