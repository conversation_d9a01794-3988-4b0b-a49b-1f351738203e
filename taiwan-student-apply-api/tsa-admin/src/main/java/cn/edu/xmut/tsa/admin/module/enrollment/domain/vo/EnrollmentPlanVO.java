package cn.edu.xmut.tsa.admin.module.enrollment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 招生计划视图对象
 *
 * <AUTHOR>
 * @Date 2025-06-20
 */
@Data
@Schema(description = "招生计划视图对象")
public class EnrollmentPlanVO {

    /**
     * 招生计划ID
     */
    @Schema(description = "招生计划ID")
    private Long enrollmentPlanId;

    /**
     * 招生年度
     */
    @Schema(description = "招生年度")
    private Integer year;

    /**
     * 科类
     */
    @Schema(description = "科类")
    private Integer subjectType;

    /**
     * 科类名称
     */
    @Schema(description = "科类名称")
    private String subjectTypeName;

    /**
     * 专业名称
     */
    @Schema(description = "专业名称")
    private String majorName;

    /**
     * 所属学院
     */
    @Schema(description = "所属学院")
    private String collegeName;

    /**
     * 学制（年）
     */
    @Schema(description = "学制（年）")
    private Integer educationDuration;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 计划数量（用于统计）
     */
    @Schema(description = "计划数量")
    private Integer planCount;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
