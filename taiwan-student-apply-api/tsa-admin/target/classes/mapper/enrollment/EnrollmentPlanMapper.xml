<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.admin.module.enrollment.dao.EnrollmentPlanDao">

    <!-- 分页查询招生计划列表 -->
    <select id="queryPage" resultType="cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            remark,
            sort,
            create_time,
            update_time
        FROM t_enrollment_plan
        <where>
            deleted_flag = 0
            <!-- 年度查询 -->
            <if test="queryForm.year != null">
                AND year = #{queryForm.year}
            </if>
            <!-- 科类查询 -->
            <if test="queryForm.subjectType != null">
                AND subject_type = #{queryForm.subjectType}
            </if>
            <!-- 专业名称查询 -->
            <if test="queryForm.majorName != null and queryForm.majorName != ''">
                AND INSTR(major_name, #{queryForm.majorName})
            </if>
            <!-- 所属学院查询 -->
            <if test="queryForm.collegeName != null and queryForm.collegeName != ''">
                AND INSTR(college_name, #{queryForm.collegeName})
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询招生计划列表（用于导出） -->
    <select id="queryList" resultType="cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            remark,
            sort,
            create_time,
            update_time
        FROM t_enrollment_plan
        <where>
            deleted_flag = 0
            <!-- 年度查询 -->
            <if test="queryForm.year != null">
                AND year = #{queryForm.year}
            </if>
            <!-- 科类查询 -->
            <if test="queryForm.subjectType != null">
                AND subject_type = #{queryForm.subjectType}
            </if>
            <!-- 专业名称查询 -->
            <if test="queryForm.majorName != null and queryForm.majorName != ''">
                AND INSTR(major_name, #{queryForm.majorName})
            </if>
            <!-- 所属学院查询 -->
            <if test="queryForm.collegeName != null and queryForm.collegeName != ''">
                AND INSTR(college_name, #{queryForm.collegeName})
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据年度查询招生计划实体列表 -->
    <select id="queryByYearEntity" resultType="cn.edu.xmut.tsa.admin.module.enrollment.domain.entity.EnrollmentPlanEntity">
        SELECT
            enrollment_plan_id,
            year,
            subject_type,
            major_name,
            college_name,
            education_duration,
            remark,
            sort,
            create_time,
            update_time
        FROM t_enrollment_plan
        WHERE year = #{year} AND deleted_flag = 0
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询所有年度列表 -->
    <select id="queryYearList" resultType="java.lang.Integer">
        SELECT DISTINCT year
        FROM t_enrollment_plan
        WHERE deleted_flag = 0
        ORDER BY year DESC
    </select>

    <!-- 根据年度统计招生计划数量 -->
    <select id="countByYear" resultType="cn.edu.xmut.tsa.admin.module.enrollment.domain.vo.EnrollmentPlanVO">
        SELECT
            year,
            COUNT(*) as planCount
        FROM t_enrollment_plan
        WHERE deleted_flag = 0
        GROUP BY year
        ORDER BY year DESC
    </select>

    <!-- 检查专业是否存在（用于导入时重复性校验） -->
    <select id="checkMajorExists" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM t_enrollment_plan
        WHERE year = #{year}
          AND major_name = #{majorName}
          AND college_name = #{collegeName}
          AND deleted_flag = 0
    </select>

    <!-- 批量插入招生计划 -->
    <insert id="batchInsert">
        INSERT INTO t_enrollment_plan (
            year, subject_type, major_name, college_name, education_duration,
            remark, sort, deleted_flag, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.year}, #{item.subjectType}, #{item.majorName}, #{item.collegeName}, #{item.educationDuration},
                #{item.remark}, #{item.sort}, #{item.deletedFlag}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

</mapper>
