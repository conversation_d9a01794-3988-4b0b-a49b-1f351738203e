<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="tsa-student" />
        <module name="tsa-admin" />
        <module name="tsa-base" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="sa-admin" target="17" />
      <module name="sa-base" target="17" />
      <module name="ttsa-admin" target="17" />
      <module name="ttsa-base" target="17" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="sa-admin" options="-parameters" />
      <module name="sa-base" options="-parameters" />
      <module name="tsa-admin" options="-parameters" />
      <module name="tsa-base" options="-parameters" />
      <module name="tsa-parent" options="-parameters" />
      <module name="tsa-student" options="-parameters" />
      <module name="ttsa-admin" options="-parameters" />
      <module name="ttsa-base" options="-parameters" />
    </option>
  </component>
</project>