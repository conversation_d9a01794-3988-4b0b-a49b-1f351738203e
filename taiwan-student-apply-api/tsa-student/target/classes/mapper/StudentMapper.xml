<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao">

    <select id="queryByEmail" resultType="cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity">
        SELECT student_id,
               exam_number,
               login_name,
               login_pwd,
               actual_name,
               avatar,
               gender,
               phone,
               email,
               taiwan_passport_number,
               taiwan_resident_certificate_number,
               birth_place,
               birth_date,
               mailing_address,
               contact_phone,
               emergency_contact_name,
               emergency_contact_phone,
               personal_statement,
               current_status,
               pending_info,
               remark,
               deleted_flag,
               create_time,
               update_time
        FROM t_student
        WHERE email = #{email}
          AND deleted_flag = 0
    </select>

    <select id="queryByLoginName"
            resultType="cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity">
        SELECT student_id,
               exam_number,
               login_name,
               login_pwd,
               actual_name,
               avatar,
               gender,
               phone,
               email,
               taiwan_passport_number,
               taiwan_resident_certificate_number,
               birth_place,
               birth_date,
               mailing_address,
               contact_phone,
               emergency_contact_name,
               emergency_contact_phone,
               personal_statement,
               current_status,
               pending_info,
               remark,
               deleted_flag,
               create_time,
               update_time
        FROM t_student
        WHERE login_name = #{loginName}
          AND deleted_flag = 0
    </select>


    <select id="getByLoginName" resultType="cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity">
        SELECT *
        FROM t_student
        <where>
            login_name = #{loginName}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>
</mapper>
