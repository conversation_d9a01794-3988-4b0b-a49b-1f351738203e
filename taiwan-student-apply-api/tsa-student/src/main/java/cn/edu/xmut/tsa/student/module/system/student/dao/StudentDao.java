package cn.edu.xmut.tsa.student.module.system.student.dao;

import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 学生DAO
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Mapper
public interface StudentDao extends BaseMapper<StudentEntity> {

    /**
     * 根据邮箱查询学生
     *
     * @param email 邮箱
     * @return 学生信息
     */
    StudentEntity queryByEmail(@Param("email") String email);

    /**
     * 根据登录名查询学生
     *
     * @param loginName 登录名
     * @return 学生信息
     */
    StudentEntity queryByLoginName(@Param("loginName") String loginName);

    StudentEntity getByLoginName(@Param("loginName") String loginName, @Param("deletedFlag") Boolean deletedFlag);

}
