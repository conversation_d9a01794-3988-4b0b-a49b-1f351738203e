package cn.edu.xmut.tsa.student.module.system.student.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学生实体类
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_student")
public class StudentEntity {

    /**
     * 考生ID
     */
    @TableId(type = IdType.AUTO)
    private Long studentId;

    /**
     * 准考证号
     */
    private String examNumber;

    /**
     * 登录帐号
     */
    private String loginName;

    /**
     * 登录密码
     */
    private String loginPwd;

    /**
     * 考生名称
     */
    private String actualName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 台湾通行证号码
     */
    private String taiwanPassportNumber;

    /**
     * 台胞证号码
     */
    private String taiwanResidentCertificateNumber;

    /**
     * 出生地点
     */
    private String birthPlace;

    /**
     * 出生年月日
     */
    private LocalDate birthDate;

    /**
     * 邮寄地址
     */
    private String mailingAddress;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 应急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 应急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 个人陈述
     */
    private String personalStatement;

    /**
     * 当前状态：0-资料待完善，1-资料审核中，2-审核通过
     */
    private Integer currentStatus;

    /**
     * 资料待完善提示信息
     */
    private String pendingInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除：0-否，1-是
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
