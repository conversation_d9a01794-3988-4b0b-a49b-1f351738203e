package cn.edu.xmut.tsa.student.module.system.login.domain;

import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.enumeration.GenderEnum;
import cn.edu.xmut.tsa.base.common.enumeration.UserTypeEnum;
import cn.edu.xmut.tsa.base.common.swagger.SchemaEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class RequestStudent implements RequestUser, Serializable {

    @Schema(description = "考生id")
    private Long studentId;

    @SchemaEnum(UserTypeEnum.class)
    private UserTypeEnum userType;

    @Schema(description = "登录账号")
    private String loginName;

    @Schema(description = "考生名称")
    private String actualName;

    @Schema(description = "头像")
    private String avatar;

    @SchemaEnum(GenderEnum.class)
    private Integer gender;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "是否禁用")
    private Boolean disabledFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "请求ip")
    private String ip;

    @Schema(description = "请求user-agent")
    private String userAgent;

    @Override
    public Long getUserId() {
        return studentId;
    }

    @Override
    public String getUserName() {
        return actualName;
    }
}
