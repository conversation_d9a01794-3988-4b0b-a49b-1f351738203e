package cn.edu.xmut.tsa.student.module.system.login.manager;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.enumeration.UserTypeEnum;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.base.module.support.file.service.IFileStorageService;
import cn.edu.xmut.tsa.student.constant.StudentCacheConst;
import cn.edu.xmut.tsa.student.module.system.login.domain.RequestStudent;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.service.StudentService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 登录Manager
 *
 * <AUTHOR> 卓大
 * @Date 2025-05-03 22:56:34
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Slf4j
@Service
public class LoginManager {


    @Resource
    private IFileStorageService fileStorageService;

    @Resource
    private StudentService studentService;


    /**
     * 获取请求用户信息
     */
    @Cacheable(StudentCacheConst.Login.REQUEST_STUDENT)
    public RequestStudent getRequestStudent(Long requestStudentId) {
        if (requestStudentId == null) {
            return null;
        }
        // 员工基本信息
        StudentEntity studentEntity = studentService.getById(requestStudentId);
        if (studentEntity == null) {
            return null;
        }

        return this.loadLoginInfo(studentEntity);
    }

    /**
     * 获取登录的用户信息
     */
    @CachePut(value = StudentCacheConst.Login.REQUEST_STUDENT, key = "#studentEntity.studentId")
    public RequestStudent loadLoginInfo(StudentEntity studentEntity) {
        // 基础信息
        RequestStudent requestStudent = SmartBeanUtil.copy(studentEntity, RequestStudent.class);
        requestStudent.setUserType(UserTypeEnum.STUDENT);

        // 头像信息
        String avatar = studentEntity.getAvatar();
        if (StringUtils.isNotBlank(avatar)) {
            ResponseDTO<String> getFileUrl = fileStorageService.getFileUrl(avatar);
            if (BooleanUtils.isTrue(getFileUrl.getOk())) {
                requestStudent.setAvatar(getFileUrl.getData());
            }
        }
        return requestStudent;
    }

    @CacheEvict(value = {StudentCacheConst.Login.REQUEST_STUDENT}, allEntries = true)
    public void clear() {

    }


}
