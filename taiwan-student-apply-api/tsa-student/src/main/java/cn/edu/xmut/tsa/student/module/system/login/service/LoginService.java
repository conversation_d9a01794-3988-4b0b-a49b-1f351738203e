package cn.edu.xmut.tsa.student.module.system.login.service;

import cn.dev33.satoken.stp.StpInterface;
import cn.dev33.satoken.stp.StpUtil;
import cn.edu.xmut.tsa.base.common.code.UserErrorCode;
import cn.edu.xmut.tsa.base.common.constant.RequestHeaderConst;
import cn.edu.xmut.tsa.base.common.constant.StringConst;
import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.enumeration.UserTypeEnum;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.base.common.util.SmartEnumUtil;
import cn.edu.xmut.tsa.base.common.util.SmartIpUtil;
import cn.edu.xmut.tsa.base.constant.LoginDeviceEnum;
import cn.edu.xmut.tsa.base.module.support.apiencrypt.service.ApiEncryptService;
import cn.edu.xmut.tsa.base.module.support.captcha.CaptchaService;
import cn.edu.xmut.tsa.base.module.support.captcha.domain.CaptchaVO;
import cn.edu.xmut.tsa.base.module.support.loginlog.LoginLogResultEnum;
import cn.edu.xmut.tsa.base.module.support.loginlog.LoginLogService;
import cn.edu.xmut.tsa.base.module.support.loginlog.domain.LoginLogEntity;
import cn.edu.xmut.tsa.base.module.support.loginlog.domain.LoginLogVO;
import cn.edu.xmut.tsa.base.module.support.mail.MailService;
import cn.edu.xmut.tsa.base.module.support.redis.RedisService;
import cn.edu.xmut.tsa.base.module.support.securityprotect.domain.LoginFailEntity;
import cn.edu.xmut.tsa.base.module.support.securityprotect.service.Level3ProtectConfigService;
import cn.edu.xmut.tsa.base.module.support.securityprotect.service.SecurityLoginService;
import cn.edu.xmut.tsa.base.module.support.securityprotect.service.SecurityPasswordService;
import cn.edu.xmut.tsa.student.module.system.login.domain.LoginForm;
import cn.edu.xmut.tsa.student.module.system.login.domain.LoginResultVO;
import cn.edu.xmut.tsa.student.module.system.login.domain.RequestStudent;
import cn.edu.xmut.tsa.student.module.system.login.manager.LoginManager;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.service.StudentService;
import cn.hutool.extra.servlet.JakartaServletUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 登录
 *
 * <AUTHOR> 卓大
 * @Date 2025-05-03 22:56:34
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Slf4j
@Service
public class LoginService implements StpInterface {

    /**
     * 万能密码的 sa token loginId 前缀
     */
    private static final String SUPER_PASSWORD_LOGIN_ID_PREFIX = "S";

    @Resource
    private StudentService studentService;


    @Resource
    private CaptchaService captchaService;


    @Resource
    private LoginLogService loginLogService;

    @Resource
    private SecurityLoginService securityLoginService;

    @Resource
    private SecurityPasswordService protectPasswordService;

    @Resource
    private ApiEncryptService apiEncryptService;

    @Resource
    private Level3ProtectConfigService level3ProtectConfigService;

    @Resource
    private MailService mailService;

    @Resource
    private RedisService redisService;

    @Resource
    private LoginManager loginManager;

    /**
     * 获取验证码
     */
    public ResponseDTO<CaptchaVO> getCaptcha() {
        return ResponseDTO.ok(captchaService.generateCaptcha());
    }

    /**
     * 考生登陆
     *
     * @return 返回用户登录信息
     */
    public ResponseDTO<LoginResultVO> login(LoginForm loginForm, String ip, String userAgent) {

        LoginDeviceEnum loginDeviceEnum = SmartEnumUtil.getEnumByValue(loginForm.getLoginDevice(), LoginDeviceEnum.class);
        if (loginDeviceEnum == null) {
            return ResponseDTO.userErrorParam("登录设备暂不支持！");
        }

        // 验证登录名
        StudentEntity studentEntity = studentService.getByLoginName(loginForm.getLoginName());
        if (null == studentEntity) {
            return ResponseDTO.userErrorParam("登录名或密码错误！");
        }

        // 验证账号状态
        if (studentEntity.getDeletedFlag()) {
            saveLoginLog(studentEntity, ip, userAgent, "账号已删除", LoginLogResultEnum.LOGIN_FAIL, loginDeviceEnum);
            return ResponseDTO.userErrorParam("您的账号已被删除,请联系工作人员！");
        }

        // 解密前端加密的密码
        String requestPassword = apiEncryptService.decrypt(loginForm.getPassword());

        // 按照等保登录要求，进行登录失败次数校验
        ResponseDTO<LoginFailEntity> loginFailEntityResponseDTO = securityLoginService.checkLogin(studentEntity.getStudentId(), UserTypeEnum.STUDENT);
        if (!loginFailEntityResponseDTO.getOk()) {
            return ResponseDTO.error(loginFailEntityResponseDTO);
        }

        // 密码错误
        if (!SecurityPasswordService.matchesPwd(requestPassword, studentEntity.getLoginPwd())) {
            // 记录登录失败
            saveLoginLog(studentEntity, ip, userAgent, "密码错误", LoginLogResultEnum.LOGIN_FAIL, loginDeviceEnum);
            // 记录等级保护次数
            String msg = securityLoginService.recordLoginFail(studentEntity.getStudentId(), UserTypeEnum.STUDENT, studentEntity.getLoginName(), loginFailEntityResponseDTO.getData());
            return msg == null ? ResponseDTO.userErrorParam("登录名或密码错误！") : ResponseDTO.error(UserErrorCode.LOGIN_FAIL_WILL_LOCK, msg);
        }

        String saTokenLoginId = UserTypeEnum.STUDENT.getValue() + StringConst.COLON + studentEntity.getStudentId();

        // 登录
        StpUtil.login(saTokenLoginId, String.valueOf(loginDeviceEnum.getDesc()));

        // 获取员工信息
        RequestStudent requestStudent = loginManager.loadLoginInfo(studentEntity);

        // 移除登录失败
        securityLoginService.removeLoginFail(studentEntity.getStudentId(), UserTypeEnum.STUDENT);

        // 获取登录结果信息
        String token = StpUtil.getTokenValue();
        LoginResultVO loginResultVO = getLoginResult(requestStudent, token);

        //保存登录记录
        saveLoginLog(studentEntity, ip, userAgent, StringConst.EMPTY, LoginLogResultEnum.LOGIN_SUCCESS, loginDeviceEnum);

        // 设置 token
        loginResultVO.setToken(token);

        return ResponseDTO.ok(loginResultVO);
    }


    /**
     * 获取登录结果信息
     */
    public LoginResultVO getLoginResult(RequestStudent requestStudent, String token) {

        // 基础信息
        LoginResultVO loginResultVO = SmartBeanUtil.copy(requestStudent, LoginResultVO.class);

        // 前端菜单和功能点清单 - 暂时简化
        loginResultVO.setMenuList(new ArrayList<>());

        // 上次登录信息
        LoginLogVO loginLogVO = loginLogService.queryLastByUserId(requestStudent.getStudentId(), UserTypeEnum.STUDENT, LoginLogResultEnum.LOGIN_SUCCESS);
        if (loginLogVO != null) {
            loginResultVO.setLastLoginIp(loginLogVO.getLoginIp());
            loginResultVO.setLastLoginIpRegion(loginLogVO.getLoginIpRegion());
            loginResultVO.setLastLoginTime(loginLogVO.getCreateTime());
            loginResultVO.setLastLoginUserAgent(loginLogVO.getUserAgent());
        }

        // 是否需要强制修改密码
        boolean needChangePasswordFlag = protectPasswordService.checkNeedChangePassword(requestStudent.getUserType().getValue(), requestStudent.getUserId());
        loginResultVO.setNeedUpdatePwdFlag(needChangePasswordFlag);

        // 万能密码登录，则不需要设置强制修改密码
        String loginIdByToken = (String) StpUtil.getLoginIdByToken(token);
        if (loginIdByToken != null && loginIdByToken.startsWith(SUPER_PASSWORD_LOGIN_ID_PREFIX)) {
            loginResultVO.setNeedUpdatePwdFlag(false);
        }

        return loginResultVO;
    }


    public RequestStudent getLoginStudent(String loginId, HttpServletRequest request) {
        if (loginId == null) {
            return null;
        }

        Long requestStudentId = getStudentIdByLoginId(loginId);
        if (requestStudentId == null) {
            return null;
        }

        RequestStudent requestStudent = loginManager.getRequestStudent(requestStudentId);

        // 更新请求ip和user agent
        requestStudent.setUserAgent(JakartaServletUtil.getHeaderIgnoreCase(request, RequestHeaderConst.USER_AGENT));
        requestStudent.setIp(JakartaServletUtil.getClientIP(request));

        return requestStudent;
    }

    /**
     * 根据 loginId 获取 员工id
     */
    Long getStudentIdByLoginId(String loginId) {

        if (loginId == null) {
            return null;
        }

        try {
            String studentIdStr = loginId.substring(2);

            return Long.parseLong(studentIdStr);
        } catch (Exception e) {
            log.error("loginId parse error , loginId : {}", loginId, e);
            return null;
        }
    }


    /**
     * 退出登录
     */
    public ResponseDTO<String> logout(RequestUser requestUser) {

        // sa token 登出
        StpUtil.logout();

        // 清空登录信息缓存
        loginManager.clear();

        //保存登出日志
        LoginLogEntity loginEntity = LoginLogEntity.builder()
                .userId(requestUser.getUserId())
                .userType(requestUser.getUserType().getValue())
                .userName(requestUser.getUserName())
                .userAgent(requestUser.getUserAgent())
                .loginIp(requestUser.getIp())
                .loginIpRegion(SmartIpUtil.getRegion(requestUser.getIp()))
                .loginResult(LoginLogResultEnum.LOGIN_OUT.getValue())
                .createTime(LocalDateTime.now())
                .build();
        loginLogService.log(loginEntity);

        return ResponseDTO.ok();
    }

    /**
     * 保存登录日志
     */
    private void saveLoginLog(StudentEntity studentEntity, String ip, String userAgent, String remark, LoginLogResultEnum result, LoginDeviceEnum loginDeviceEnum) {
        LoginLogEntity loginEntity = LoginLogEntity.builder()
                .userId(studentEntity.getStudentId())
                .userType(UserTypeEnum.STUDENT.getValue())
                .userName(studentEntity.getActualName())
                .userAgent(userAgent)
                .loginIp(ip)
                .loginIpRegion(SmartIpUtil.getRegion(ip))
                .remark(remark)
                .loginDevice(loginDeviceEnum.getDesc())
                .loginResult(result.getValue())
                .createTime(LocalDateTime.now())
                .build();
        loginLogService.log(loginEntity);
    }


    @Override
    public List<String> getPermissionList(Object o, String s) {
        return List.of();
    }

    @Override
    public List<String> getRoleList(Object o, String s) {
        return List.of();
    }
}
