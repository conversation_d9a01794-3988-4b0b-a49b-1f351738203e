package cn.edu.xmut.tsa.student.util;

import cn.edu.xmut.tsa.base.common.domain.RequestUser;
import cn.edu.xmut.tsa.base.common.util.SmartRequestUtil;
import cn.edu.xmut.tsa.student.module.system.login.domain.RequestStudent;

/**
 * admin 端的请求工具类
 *
 * <AUTHOR>
 * @Date 2023/7/28 19:39:21
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>，Since 2012
 */
public final class AdminRequestUtil {


    public static RequestStudent getRequestUser() {
        return (RequestStudent) SmartRequestUtil.getRequestUser();
    }

    public static Long getRequestUserId() {
        RequestUser requestUser = getRequestUser();
        return null == requestUser ? null : requestUser.getUserId();
    }


}
