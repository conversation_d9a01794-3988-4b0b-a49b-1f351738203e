package cn.edu.xmut.tsa.student.module.system.student.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学生信息VO
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Data
@Schema(description = "学生信息")
public class StudentInfoVO {

    /**
     * 学生ID
     */
    @Schema(description = "学生ID")
    private Long studentId;

    /**
     * 准考证号
     */
    @Schema(description = "准考证号")
    private String examNumber;

    /**
     * 登录帐号
     */
    @Schema(description = "登录帐号")
    private String loginName;

    /**
     * 考生名称
     */
    @Schema(description = "考生名称")
    private String actualName;

    /**
     * 头像
     */
    @Schema(description = "头像")
    private String avatar;

    /**
     * 性别
     */
    @Schema(description = "性别")
    private Integer gender;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 台湾通行证号码
     */
    @Schema(description = "台湾通行证号码")
    private String taiwanPassportNumber;

    /**
     * 台胞证号码
     */
    @Schema(description = "台胞证号码")
    private String taiwanResidentCertificateNumber;

    /**
     * 出生地点
     */
    @Schema(description = "出生地点")
    private String birthPlace;

    /**
     * 出生年月日
     */
    @Schema(description = "出生年月日")
    private LocalDate birthDate;

    /**
     * 邮寄地址
     */
    @Schema(description = "邮寄地址")
    private String mailingAddress;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 应急联系人姓名
     */
    @Schema(description = "应急联系人姓名")
    private String emergencyContactName;

    /**
     * 应急联系人电话
     */
    @Schema(description = "应急联系人电话")
    private String emergencyContactPhone;

    /**
     * 个人陈述
     */
    @Schema(description = "个人陈述")
    private String personalStatement;

    /**
     * 当前状态：0-资料待完善，1-资料审核中，2-审核通过
     */
    @Schema(description = "当前状态")
    private Integer currentStatus;

    /**
     * 资料待完善提示信息
     */
    @Schema(description = "资料待完善提示信息")
    private String pendingInfo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
