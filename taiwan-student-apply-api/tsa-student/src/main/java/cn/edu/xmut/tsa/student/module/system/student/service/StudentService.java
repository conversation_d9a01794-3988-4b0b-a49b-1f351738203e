package cn.edu.xmut.tsa.student.module.system.student.service;

import cn.edu.xmut.tsa.base.common.domain.ResponseDTO;
import cn.edu.xmut.tsa.base.common.util.SmartBeanUtil;
import cn.edu.xmut.tsa.student.module.system.student.dao.StudentDao;
import cn.edu.xmut.tsa.student.module.system.student.domain.entity.StudentEntity;
import cn.edu.xmut.tsa.student.module.system.student.domain.vo.StudentInfoVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 学生信息服务
 *
 * <AUTHOR>
 * @Date 2025-06-20 11:00:00
 */
@Slf4j
@Service
public class StudentService {

    @Resource
    private StudentDao studentDao;

    /**
     * 根据学生ID获取学生信息
     *
     * @param studentId 学生ID
     * @return 学生信息
     */
    public ResponseDTO<StudentInfoVO> getStudentInfo(Long studentId) {
        StudentEntity studentEntity = studentDao.selectById(studentId);
        if (studentEntity == null || studentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("学生信息不存在");
        }

        StudentInfoVO studentInfoVO = SmartBeanUtil.copy(studentEntity, StudentInfoVO.class);
        return ResponseDTO.ok(studentInfoVO);
    }

    public StudentEntity getById(Long studentId) {
        return studentDao.selectById(studentId);
    }


    public StudentEntity getByLoginName(String loginName) {
        return studentDao.getByLoginName(loginName, false);
    }
}
