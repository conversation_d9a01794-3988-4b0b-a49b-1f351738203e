# TSA-Student 学生端模块

## 概述

TSA-Student 是台湾学生申请系统的学生端模块，提供学生注册、登录、找回密码等功能。

## 功能特性

### 1. 学生注册

- 通过邮箱和邮箱验证码注册
- 密码加密存储
- 自动生成准考证号
- 注册信息保存到 `t_student` 表

### 2. 学生登录

- 支持邮箱登录
- 密码验证
- 登录失败次数限制
- 验证码支持
- 登录日志记录

### 3. 找回密码

- 通过邮箱和邮箱验证码找回密码
- 密码重置功能

### 4. 其他功能

- 退出登录
- 获取验证码
- 发送邮箱验证码

## API 接口

### 注册接口

```
POST /student/register
```

### 登录接口

```
POST /student/login
```

### 找回密码接口

```
POST /student/reset-password
```

### 退出登录接口

```
POST /student/logout
```

### 获取验证码接口

```
GET /student/captcha
```

### 发送邮箱验证码接口

```
POST /student/send-email-code
```

## 项目结构

```
tsa-student/
├── src/main/java/cn/edu/xmut/tsa/student/
│   ├── StudentApplication.java                 # 启动类
│   ├── config/                                 # 配置类
│   │   └── StudentMvcConfig.java              # Web配置
│   ├── constant/                               # 常量类
│   │   └── StudentConstant.java               # 学生常量
│   ├── interceptor/                            # 拦截器
│   │   └── StudentInterceptor.java            # 学生端拦截器
│   ├── module/                                 # 业务模块
│   │   ├── login/                             # 登录模块
│   │   │   ├── controller/                    # 控制器
│   │   │   ├── domain/                        # 领域对象
│   │   │   └── service/                       # 服务类
│   │   └── student/                           # 学生模块
│   │       ├── dao/                           # 数据访问层
│   │       └── domain/                        # 领域对象
│   └── util/                                  # 工具类
│       └── StudentRequestUtil.java           # 请求工具类
└── src/main/resources/
    ├── application.yaml                       # 配置文件
    └── mapper/                               # MyBatis映射文件
        └── StudentMapper.xml
```

## 配置说明

### 端口配置

- 默认端口：1025
- 上下文路径：/

### 数据库表

- `t_student`：学生信息表

## 启动方式

```bash
java -jar tsa-student-3.0.0.jar
```

## 依赖项目

- tsa-base：基础模块，包含通用功能和配置

## 注意事项

1. 密码在前端加密传输，后端解密后再次加密存储
2. 邮箱验证码有效期为 5 分钟
3. 登录失败次数达到限制后会锁定账户
4. 所有接口都支持 Swagger 文档
