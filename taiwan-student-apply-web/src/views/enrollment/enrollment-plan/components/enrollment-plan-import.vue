<template>
  <a-modal title="导入招生计划" :open="visible" :width="600" :confirm-loading="confirmLoading" @ok="onSubmit" @cancel="onClose">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="导入年度" name="year">
        <a-input-number v-model:value="form.year" :min="2000" :max="2100" :disabled="true" style="width: 100%" />
      </a-form-item>

      <a-form-item label="选择文件" name="file">
        <a-upload :file-list="fileList" :before-upload="beforeUpload" :remove="removeFile" accept=".xlsx,.xls">
          <a-button>
            <template #icon>
              <UploadOutlined />
            </template>
            选择Excel文件
          </a-button>
        </a-upload>
        <div class="upload-tips">
          <p>支持格式：.xlsx、.xls</p>
          <p>文件大小：不超过10MB</p>
        </div>
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
        <a-button type="link" @click="downloadTemplate">
          <template #icon>
            <DownloadOutlined />
          </template>
          下载导入模板
        </a-button>
      </a-form-item>

      <a-alert message="导入说明" type="info" show-icon>
        <template #description>
          <ul>
            <li>请先下载导入模板，按照模板格式填写数据</li>
            <li>科类只支持：文史科、理工科</li>
            <li>学制范围：1-8年</li>
            <li>招生人数范围：0-9999</li>
            <li>同一年度同一学院的专业名称不能重复</li>
          </ul>
        </template>
      </a-alert>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { UploadOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import { enrollmentPlanApi } from '../../../../api/enrollment/enrollment-plan-api';

  // ----------------------- props -----------------------
  const props = defineProps({
    selectedYear: {
      type: Number,
      default: null,
    },
  });

  // ----------------------- emit -----------------------
  const emit = defineEmits(['refresh']);

  // ----------------------- 响应式数据 -----------------------

  // 组件引用
  const formRef = ref();

  // 显示、隐藏操作的变量和方法
  const visible = ref(false);
  const confirmLoading = ref(false);

  // 文件列表
  const fileList = ref([]);

  // 表单数据
  const formDefault = {
    year: undefined,
    file: undefined,
  };

  const form = reactive({ ...formDefault });

  // 表单验证规则
  const rules = {
    year: [{ required: true, message: '请选择导入年度' }],
    file: [{ required: true, message: '请选择要导入的文件' }],
  };

  // ----------------------- 监听 -----------------------

  watch(
    () => props.selectedYear,
    (newYear) => {
      if (newYear) {
        form.year = newYear;
      }
    },
    { immediate: true }
  );

  // ----------------------- 方法 -----------------------

  /**
   * 文件上传前处理
   */
  function beforeUpload(file) {
    // 检查文件类型 - 通过文件名后缀判断
    const fileName = file.name.toLowerCase();
    const isValidType = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
    if (!isValidType) {
      message.error('只支持 Excel 文件格式（.xlsx、.xls）');
      return false;
    }

    // 检查文件大小
    const isValidSize = file.size <= 10 * 1024 * 1024;
    if (!isValidSize) {
      message.error('文件大小不能超过 10MB');
      return false;
    }

    // 设置文件
    form.file = file;
    fileList.value = [file];

    return false; // 阻止自动上传
  }

  /**
   * 移除文件
   */
  function removeFile() {
    form.file = null;
    fileList.value = [];
  }

  /**
   * 下载导入模板
   */
  async function downloadTemplate() {
    await enrollmentPlanApi.downloadTemplate();
  }

  /**
   * 显示弹窗
   */
  function showModal() {
    // 重置表单
    Object.assign(form, formDefault);
    form.year = props.selectedYear;
    fileList.value = [];

    visible.value = true;

    // 清除表单验证
    setTimeout(() => {
      formRef.value?.clearValidate();
    }, 100);
  }

  /**
   * 关闭弹窗
   */
  function onClose() {
    visible.value = false;
    confirmLoading.value = false;
    Object.assign(form, formDefault);
    fileList.value = [];
  }

  /**
   * 提交表单
   */
  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        if (!form.file) {
          message.error('请选择要导入的文件');
          return;
        }

        confirmLoading.value = true;
        try {
          // 创建FormData
          const formData = new FormData();
          formData.append('file', form.file);
          formData.append('year', form.year);

          await enrollmentPlanApi.importExcel(formData);
          message.success('导入成功');

          emit('refresh');
          onClose();
        } catch (error) {
          message.error('导入失败：' + error.message);
        } finally {
          confirmLoading.value = false;
        }
      })
      .catch((error) => {
        console.log('表单验证失败:', error);
        message.error('请检查表单数据');
      });
  }

  // ----------------------- 暴露方法 -----------------------

  defineExpose({
    showModal,
  });
</script>

<style scoped>
  .ant-form-item {
    margin-bottom: 16px;
  }

  .upload-tips {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
  }

  .upload-tips p {
    margin: 0;
    line-height: 1.4;
  }
</style>
