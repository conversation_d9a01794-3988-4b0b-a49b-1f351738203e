<template>
  <a-modal title="招生计划详情" :open="visible" :width="800" :footer="null" @cancel="onClose">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="招生年度"> {{ detail.year }}年 </a-descriptions-item>

      <a-descriptions-item label="科类">
        {{ $smartEnumPlugin.getDescByValue('SUBJECT_TYPE_ENUM', detail.subjectType) }}
      </a-descriptions-item>

      <a-descriptions-item label="专业名称" :span="2">
        {{ detail.majorName }}
      </a-descriptions-item>

      <a-descriptions-item label="所属学院" :span="2">
        {{ detail.collegeName }}
      </a-descriptions-item>

      <a-descriptions-item label="学制"> {{ detail.educationDuration }}年 </a-descriptions-item>

      <a-descriptions-item label="排序">
        {{ detail.sort }}
      </a-descriptions-item>

      <a-descriptions-item label="创建时间">
        {{ formatDateTime(detail.createTime) }}
      </a-descriptions-item>

      <a-descriptions-item label="更新时间" :span="2">
        {{ formatDateTime(detail.updateTime) }}
      </a-descriptions-item>

      <a-descriptions-item label="备注" :span="2">
        {{ detail.remark || '无' }}
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { formatDateTime } from '/@/utils/date-util';

  // ----------------------- 响应式数据 -----------------------

  // 显示、隐藏操作的变量和方法
  const visible = ref(false);

  // 详情数据
  const detail = reactive({
    enrollmentPlanId: null,
    year: null,
    subjectType: null,
    majorName: '',
    collegeName: '',
    educationDuration: null,
    sort: null,
    remark: '',
    createTime: null,
    updateTime: null,
  });

  // ----------------------- 方法 -----------------------

  /**
   * 显示弹窗
   */
  function showModal(record) {
    if (record) {
      Object.assign(detail, record);
    }
    visible.value = true;
  }

  /**
   * 关闭弹窗
   */
  function onClose() {
    visible.value = false;
    // 清空详情数据
    Object.keys(detail).forEach((key) => {
      detail[key] = null;
    });
  }

  // ----------------------- 暴露方法 -----------------------

  defineExpose({
    showModal,
  });
</script>

<style scoped>
  .ant-descriptions {
    margin-top: 16px;
  }
</style>
